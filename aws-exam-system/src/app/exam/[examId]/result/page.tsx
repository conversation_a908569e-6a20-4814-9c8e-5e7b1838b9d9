"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { getExamResult } from "@/lib/actions/exam-actions";
import { formatTime, formatPercentage } from "@/lib/utils";
import { Trophy, Target, BarChart3, Home, RotateCcw } from "lucide-react";

interface ResultPageProps {
  params: {
    examId: string;
  };
}

export default function ResultPage({ params }: ResultPageProps) {
  const router = useRouter();
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadResult();
  }, [params.examId]);

  const loadResult = async () => {
    try {
      const response = await getExamResult(params.examId);
      if (response.success) {
        setResult(response.result);
      } else {
        alert(response.error || "加载结果失败");
        router.push("/");
      }
    } catch (error) {
      console.error("加载结果失败:", error);
      alert("加载结果失败");
      router.push("/");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="text-gray-600">加载结果中...</p>
        </div>
      </div>
    );
  }

  if (!result) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">结果不存在</p>
          <Button onClick={() => router.push("/")} className="mt-4">
            返回首页
          </Button>
        </div>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-green-100";
    if (score >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  const isPassed = result.score >= 72; // AWS SAA-C03 passing score

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-4xl">
          {/* Header */}
          <div className="mb-8 text-center">
            <div
              className={`mb-4 inline-flex h-20 w-20 items-center justify-center rounded-full ${
                isPassed ? "bg-green-100" : "bg-red-100"
              }`}
            >
              <Trophy
                className={`h-10 w-10 ${isPassed ? "text-green-600" : "text-red-600"}`}
              />
            </div>
            <h1 className="mb-2 text-3xl font-bold text-gray-900">
              考试完成！
            </h1>
            <p
              className={`text-xl ${isPassed ? "text-green-600" : "text-red-600"}`}
            >
              {isPassed ? "🎉 恭喜通过！" : "💪 继续努力！"}
            </p>
          </div>

          {/* Score Overview */}
          <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-4">
            <div
              className={`rounded-lg bg-white p-6 text-center shadow-sm ${getScoreBgColor(result.score)}`}
            >
              <div
                className={`mb-2 text-3xl font-bold ${getScoreColor(result.score)}`}
              >
                {Math.round(result.score)}%
              </div>
              <div className="text-gray-600">总分</div>
            </div>

            <div className="rounded-lg bg-white p-6 text-center shadow-sm">
              <div className="mb-2 text-3xl font-bold text-blue-600">
                {result.correctCount}
              </div>
              <div className="text-gray-600">正确题数</div>
            </div>

            <div className="rounded-lg bg-white p-6 text-center shadow-sm">
              <div className="mb-2 text-3xl font-bold text-purple-600">
                {result.totalQuestions}
              </div>
              <div className="text-gray-600">总题数</div>
            </div>

            <div className="rounded-lg bg-white p-6 text-center shadow-sm">
              <div className="mb-2 text-3xl font-bold text-orange-600">
                {formatTime(result.timeSpent)}
              </div>
              <div className="text-gray-600">用时</div>
            </div>
          </div>

          {/* Category Analysis */}
          <div className="mb-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
                <BarChart3 className="h-5 w-5" />
                分类分析
              </h2>
              <div className="space-y-4">
                {result.categoryResults.map((category: any) => (
                  <div key={category.category}>
                    <div className="mb-2 flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        {category.category}
                      </span>
                      <span className="text-sm text-gray-600">
                        {category.correct}/{category.total} (
                        {formatPercentage(category.percentage)})
                      </span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-gray-200">
                      <div
                        className={`h-2 rounded-full ${
                          category.percentage >= 80
                            ? "bg-green-500"
                            : category.percentage >= 60
                              ? "bg-yellow-500"
                              : "bg-red-500"
                        }`}
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Difficulty Analysis */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h2 className="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
                <Target className="h-5 w-5" />
                难度分析
              </h2>
              <div className="space-y-4">
                {result.difficultyResults.map((difficulty: any) => (
                  <div key={difficulty.difficulty}>
                    <div className="mb-2 flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        {difficulty.difficulty}
                      </span>
                      <span className="text-sm text-gray-600">
                        {difficulty.correct}/{difficulty.total} (
                        {formatPercentage(difficulty.percentage)})
                      </span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-gray-200">
                      <div
                        className={`h-2 rounded-full ${
                          difficulty.percentage >= 80
                            ? "bg-green-500"
                            : difficulty.percentage >= 60
                              ? "bg-yellow-500"
                              : "bg-red-500"
                        }`}
                        style={{ width: `${difficulty.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Performance Insights */}
          <div className="mb-8 rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              📊 成绩分析
            </h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-2 font-medium text-gray-900">优势领域</h3>
                <div className="space-y-2">
                  {result.categoryResults
                    .filter((cat: any) => cat.percentage >= 80)
                    .slice(0, 3)
                    .map((cat: any) => (
                      <div
                        key={cat.category}
                        className="flex items-center gap-2"
                      >
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span className="text-sm text-gray-700">
                          {cat.category}
                        </span>
                        <span className="ml-auto text-sm text-green-600">
                          {formatPercentage(cat.percentage)}
                        </span>
                      </div>
                    ))}
                  {result.categoryResults.filter(
                    (cat: any) => cat.percentage >= 80
                  ).length === 0 && (
                    <p className="text-sm text-gray-500">
                      暂无优势领域，继续努力！
                    </p>
                  )}
                </div>
              </div>

              <div>
                <h3 className="mb-2 font-medium text-gray-900">需要改进</h3>
                <div className="space-y-2">
                  {result.categoryResults
                    .filter((cat: any) => cat.percentage < 60)
                    .slice(0, 3)
                    .map((cat: any) => (
                      <div
                        key={cat.category}
                        className="flex items-center gap-2"
                      >
                        <div className="h-2 w-2 rounded-full bg-red-500"></div>
                        <span className="text-sm text-gray-700">
                          {cat.category}
                        </span>
                        <span className="ml-auto text-sm text-red-600">
                          {formatPercentage(cat.percentage)}
                        </span>
                      </div>
                    ))}
                  {result.categoryResults.filter(
                    (cat: any) => cat.percentage < 60
                  ).length === 0 && (
                    <p className="text-sm text-gray-500">各领域表现均衡！</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Link href="/">
              <Button variant="outline" className="flex items-center gap-2">
                <Home className="h-4 w-4" />
                返回首页
              </Button>
            </Link>

            <Link href="/exam/create?type=practice">
              <Button className="flex items-center gap-2">
                <RotateCcw className="h-4 w-4" />
                继续练习
              </Button>
            </Link>

            {!isPassed && (
              <Link href="/exam/create?type=simulation">
                <Button variant="outline" className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  重新模拟考试
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
