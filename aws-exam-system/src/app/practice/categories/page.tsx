"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { QuestionCategory } from "@/types/exam";
import {
  Database,
  Server,
  HardDrive,
  Zap,
  Network,
  Shield,
  BarChart3,
  Globe,
  Settings,
  MessageSquare,
  Wrench,
  DollarSign,
  BookOpen,
} from "lucide-react";

const CATEGORY_INFO: Record<
  QuestionCategory,
  {
    icon: React.ReactNode;
    description: string;
    color: string;
    bgColor: string;
  }
> = {
  Storage: {
    icon: <HardDrive className="h-8 w-8" />,
    description: "S3, EBS, EFS, FSx 等存储服务",
    color: "text-blue-600",
    bgColor: "bg-blue-100",
  },
  Compute: {
    icon: <Server className="h-8 w-8" />,
    description: "EC2, Auto Scaling, Load Balancer",
    color: "text-green-600",
    bgColor: "bg-green-100",
  },
  Database: {
    icon: <Database className="h-8 w-8" />,
    description: "R<PERSON>, DynamoDB, ElastiCache",
    color: "text-purple-600",
    bgColor: "bg-purple-100",
  },
  Serverless: {
    icon: <Zap className="h-8 w-8" />,
    description: "Lambda, API Gateway, Step Functions",
    color: "text-yellow-600",
    bgColor: "bg-yellow-100",
  },
  Networking: {
    icon: <Network className="h-8 w-8" />,
    description: "VPC, Route 53, Direct Connect",
    color: "text-indigo-600",
    bgColor: "bg-indigo-100",
  },
  Security: {
    icon: <Shield className="h-8 w-8" />,
    description: "IAM, KMS, WAF, GuardDuty",
    color: "text-red-600",
    bgColor: "bg-red-100",
  },
  Monitoring: {
    icon: <BarChart3 className="h-8 w-8" />,
    description: "CloudWatch, X-Ray, CloudTrail",
    color: "text-orange-600",
    bgColor: "bg-orange-100",
  },
  "Content Delivery": {
    icon: <Globe className="h-8 w-8" />,
    description: "CloudFront, Global Accelerator",
    color: "text-teal-600",
    bgColor: "bg-teal-100",
  },
  "API Management": {
    icon: <Settings className="h-8 w-8" />,
    description: "API Gateway, AppSync",
    color: "text-cyan-600",
    bgColor: "bg-cyan-100",
  },
  Messaging: {
    icon: <MessageSquare className="h-8 w-8" />,
    description: "SQS, SNS, EventBridge",
    color: "text-pink-600",
    bgColor: "bg-pink-100",
  },
  Management: {
    icon: <Wrench className="h-8 w-8" />,
    description: "CloudFormation, Systems Manager",
    color: "text-gray-600",
    bgColor: "bg-gray-100",
  },
  "Cost Management": {
    icon: <DollarSign className="h-8 w-8" />,
    description: "成本优化和计费管理",
    color: "text-emerald-600",
    bgColor: "bg-emerald-100",
  },
  General: {
    icon: <BookOpen className="h-8 w-8" />,
    description: "综合知识和最佳实践",
    color: "text-slate-600",
    bgColor: "bg-slate-100",
  },
};

export default function CategoriesPage() {
  const [categoryStats, setCategoryStats] = useState<Record<string, number>>(
    {}
  );

  useEffect(() => {
    // 这里可以添加获取每个分类题目数量的逻辑
    // 暂时使用模拟数据
    setCategoryStats({
      Storage: 156,
      Compute: 142,
      Database: 98,
      Serverless: 87,
      Networking: 134,
      Security: 123,
      Monitoring: 76,
      "Content Delivery": 45,
      "API Management": 34,
      Messaging: 67,
      Management: 89,
      "Cost Management": 43,
      General: 124,
    });
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-6xl">
          {/* Header */}
          <div className="mb-12 text-center">
            <h1 className="mb-4 text-4xl font-bold text-gray-900">分类练习</h1>
            <p className="mx-auto max-w-2xl text-xl text-gray-600">
              选择AWS服务分类进行专项练习，针对性提升薄弱环节
            </p>
          </div>

          {/* Categories Grid */}
          <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(CATEGORY_INFO).map(([category, info]) => (
              <Link
                key={category}
                href={`/exam/create?type=practice&category=${category}`}
                className="block"
              >
                <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:border-gray-300 hover:shadow-md">
                  <div
                    className={`mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full ${info.bgColor}`}
                  >
                    <div className={info.color}>{info.icon}</div>
                  </div>

                  <h3 className="mb-2 text-xl font-semibold text-gray-900">
                    {category}
                  </h3>

                  <p className="mb-4 text-sm leading-relaxed text-gray-600">
                    {info.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {categoryStats[category] || 0} 道题目
                    </span>
                    <Button size="sm" variant="outline">
                      开始练习
                    </Button>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              快速开始
            </h2>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <Link href="/exam/create?type=practice&difficulty=Easy">
                <Button variant="outline" className="w-full justify-start">
                  <div className="mr-2 h-3 w-3 rounded-full bg-green-500"></div>
                  简单题目练习
                </Button>
              </Link>

              <Link href="/exam/create?type=practice&difficulty=Medium">
                <Button variant="outline" className="w-full justify-start">
                  <div className="mr-2 h-3 w-3 rounded-full bg-yellow-500"></div>
                  中等题目练习
                </Button>
              </Link>

              <Link href="/exam/create?type=practice&difficulty=Hard">
                <Button variant="outline" className="w-full justify-start">
                  <div className="mr-2 h-3 w-3 rounded-full bg-red-500"></div>
                  困难题目练习
                </Button>
              </Link>
            </div>
          </div>

          {/* Back to Home */}
          <div className="mt-8 text-center">
            <Link href="/">
              <Button variant="outline">返回首页</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
