"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { getExamReview, saveAnswerCorrection } from "@/lib/actions/exam-actions";
import {
  CheckCircle,
  XCircle,
  ArrowLeft,
  ArrowRight,
  Home,
  Edit,
  Save,
  X,
} from "lucide-react";

interface QuestionReview {
  id: string;
  questionNumber: number;
  questionText: string;
  options: string[];
  correctAnswer: string;
  userAnswer: string;
  isCorrect: boolean;
  isMultipleChoice: boolean;
  multipleChoiceCount?: number;
  category: string | null;
  difficulty: string | null;
  explanation?: string;
}

interface ExamReviewProps {
  params: {
    examId: string;
  };
}

export default function ExamReviewPage({ params }: ExamReviewProps) {
  const router = useRouter();
  const [questions, setQuestions] = useState<QuestionReview[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [filter, setFilter] = useState<"all" | "correct" | "incorrect">("all");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadExamReview();
  }, [params.examId]);

  const loadExamReview = async () => {
    try {
      const result = await getExamReview(params.examId);
      if (result.success && result.questions) {
        setQuestions(result.questions);
      } else {
        console.error("加载考试回顾失败:", result.error);
        alert("加载考试回顾失败: " + result.error);
        router.push("/history");
      }
    } catch (error) {
      console.error("加载考试回顾失败:", error);
      alert("加载考试回顾失败");
      router.push("/history");
    } finally {
      setLoading(false);
    }
  };

  const filteredQuestions = questions.filter(q => {
    if (filter === "correct") return q.isCorrect;
    if (filter === "incorrect") return !q.isCorrect;
    return true;
  });

  const currentQuestion = filteredQuestions[currentIndex];

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < filteredQuestions.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const getAnswerStatus = (optionLetter: string) => {
    const isCorrect = currentQuestion.correctAnswer.includes(optionLetter);
    const isUserSelected = currentQuestion.userAnswer.includes(optionLetter);

    if (isCorrect && isUserSelected) return "correct-selected";
    if (isCorrect && !isUserSelected) return "correct-missed";
    if (!isCorrect && isUserSelected) return "incorrect-selected";
    return "normal";
  };

  const getOptionStyle = (status: string) => {
    switch (status) {
      case "correct-selected":
        return "border-green-500 bg-green-50 text-green-900";
      case "correct-missed":
        return "border-green-500 bg-green-100 text-green-900";
      case "incorrect-selected":
        return "border-red-500 bg-red-50 text-red-900";
      default:
        return "border-gray-200 bg-white text-gray-900";
    }
  };

  const getOptionIcon = (status: string) => {
    switch (status) {
      case "correct-selected":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "correct-missed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "incorrect-selected":
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="text-gray-600">加载题目回顾中...</p>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <p className="mb-4 text-gray-600">没有找到题目数据</p>
          <Link href="/history">
            <Button>返回历史记录</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/history">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  返回历史
                </Button>
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  题目回顾
                </h1>
                <p className="text-sm text-gray-600">
                  题目 {currentIndex + 1} / {filteredQuestions.length}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Filter */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">筛选:</span>
                <select
                  value={filter}
                  onChange={e => {
                    setFilter(e.target.value as any);
                    setCurrentIndex(0);
                  }}
                  className="rounded border border-gray-300 px-2 py-1 text-sm"
                >
                  <option value="all">全部题目</option>
                  <option value="correct">答对题目</option>
                  <option value="incorrect">答错题目</option>
                </select>
              </div>

              <Link href="/">
                <Button variant="outline" size="sm">
                  <Home className="mr-2 h-4 w-4" />
                  首页
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-4xl">
          {/* Question Card */}
          <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
            {/* Question Header */}
            <div className="mb-6 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-500">
                  题目 {currentQuestion.questionNumber}
                </span>
                {currentQuestion.isMultipleChoice && (
                  <span className="rounded bg-orange-100 px-2 py-1 text-xs text-orange-700">
                    多选题 (选择 {currentQuestion.multipleChoiceCount} 项)
                  </span>
                )}
                <span className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-700">
                  {currentQuestion.category}
                </span>
                <span className="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700">
                  {currentQuestion.difficulty}
                </span>
              </div>

              <div
                className={`flex items-center gap-2 ${
                  currentQuestion.isCorrect ? "text-green-600" : "text-red-600"
                }`}
              >
                {currentQuestion.isCorrect ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <XCircle className="h-5 w-5" />
                )}
                <span className="font-medium">
                  {currentQuestion.isCorrect ? "答对" : "答错"}
                </span>
              </div>
            </div>

            {/* Question Text */}
            <div className="mb-6">
              <p className="leading-relaxed text-gray-900">
                {currentQuestion.questionText}
              </p>
            </div>

            {/* Options */}
            <div className="mb-6 space-y-3">
              {currentQuestion.options.map((option, index) => {
                const optionLetter = option.charAt(0);
                const status = getAnswerStatus(optionLetter);

                return (
                  <div
                    key={index}
                    className={`rounded-lg border p-4 ${getOptionStyle(status)}`}
                  >
                    <div className="flex items-start gap-3">
                      {getOptionIcon(status)}
                      <span className="flex-1">{option}</span>
                      {status === "correct-missed" && (
                        <span className="text-xs font-medium text-green-600">
                          正确答案
                        </span>
                      )}
                      {status === "incorrect-selected" && (
                        <span className="text-xs font-medium text-red-600">
                          您的选择
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Answer Summary */}
            <div className="mb-6 rounded-lg bg-gray-50 p-4">
              <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                <div>
                  <span className="font-medium text-gray-700">正确答案: </span>
                  <span className="font-medium text-green-600">
                    {currentQuestion.correctAnswer.split("").join(", ")}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">您的答案: </span>
                  <span
                    className={`font-medium ${
                      currentQuestion.isCorrect
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {currentQuestion.userAnswer || "未作答"}
                  </span>
                </div>
              </div>
            </div>

            {/* Explanation */}
            {currentQuestion.explanation && (
              <div className="rounded-lg bg-blue-50 p-4">
                <h4 className="mb-2 font-medium text-blue-900">解析</h4>
                <p className="text-sm leading-relaxed text-blue-800">
                  {currentQuestion.explanation}
                </p>
              </div>
            )}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentIndex === 0}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              上一题
            </Button>

            <div className="text-sm text-gray-600">
              {currentIndex + 1} / {filteredQuestions.length}
            </div>

            <Button
              onClick={handleNext}
              disabled={currentIndex === filteredQuestions.length - 1}
            >
              下一题
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
