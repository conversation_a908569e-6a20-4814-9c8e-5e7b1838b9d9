#!/usr/bin/env tsx
/**
 * 安全的导入AWS题库数据到数据库 - 只在数据库为空时导入
 */

import { PrismaClient } from "@prisma/client";
import fs from "fs";
import path from "path";

const prisma = new PrismaClient();

interface QuestionData {
  topic: string;
  question_number: string;
  question_text: string;
  options: string[];
  is_multiple_choice: boolean;
  multiple_choice_count: number;
  correct_answer: string;
  selected_answer?: string;
}

// AWS服务分类映射
function categorizeQuestion(text: string): string {
  const lowerText = text.toLowerCase();

  if (
    lowerText.includes("s3") ||
    lowerText.includes("storage") ||
    lowerText.includes("glacier") ||
    lowerText.includes("ebs") ||
    lowerText.includes("efs")
  )
    return "Storage";
  if (
    lowerText.includes("ec2") ||
    lowerText.includes("lambda") ||
    lowerText.includes("fargate") ||
    lowerText.includes("batch")
  )
    return "Compute";
  if (
    lowerText.includes("rds") ||
    lowerText.includes("dynamodb") ||
    lowerText.includes("aurora") ||
    lowerText.includes("redshift")
  )
    return "Database";
  if (
    lowerText.includes("vpc") ||
    lowerText.includes("subnet") ||
    lowerText.includes("route") ||
    lowerText.includes("nat") ||
    lowerText.includes("internet gateway")
  )
    return "Networking";
  if (
    lowerText.includes("iam") ||
    lowerText.includes("security") ||
    lowerText.includes("access")
  )
    return "Security";
  if (lowerText.includes("cloudwatch") || lowerText.includes("monitoring"))
    return "Monitoring";
  if (lowerText.includes("cloudfront") || lowerText.includes("cdn"))
    return "Content Delivery";
  if (lowerText.includes("api gateway") || lowerText.includes("rest api"))
    return "API Management";
  if (
    lowerText.includes("sqs") ||
    lowerText.includes("sns") ||
    lowerText.includes("kinesis")
  )
    return "Messaging";
  if (lowerText.includes("organizations") || lowerText.includes("account"))
    return "Management";
  if (lowerText.includes("cost") || lowerText.includes("billing"))
    return "Cost Management";

  return "General";
}

// 判断题目难度
function getDifficulty(text: string): string {
  const lowerText = text.toLowerCase();

  // 包含复杂概念的为困难
  if (
    lowerText.includes("cross-region") ||
    lowerText.includes("multi-az") ||
    lowerText.includes("encryption") ||
    lowerText.includes("compliance") ||
    lowerText.includes("disaster recovery") ||
    lowerText.includes("high availability")
  ) {
    return "Hard";
  }

  // 包含配置、设置等的为中等
  if (
    lowerText.includes("configure") ||
    lowerText.includes("setup") ||
    lowerText.includes("implement") ||
    lowerText.includes("deploy")
  ) {
    return "Medium";
  }

  return "Easy";
}

async function importQuestions() {
  try {
    // 检查数据库是否已有数据
    const existingQuestions = await prisma.question.count();

    if (existingQuestions > 0) {
      console.log(`📚 数据库已有 ${existingQuestions} 道题目，跳过导入`);
      console.log("💡 如需重新导入，请先手动清空数据库或删除容器卷");
      return;
    }

    console.log("🚀 开始导入AWS题库数据...");

    // 读取题库数据
    const dataPath = path.join(process.cwd(), "aws_quiz_final.json");

    if (!fs.existsSync(dataPath)) {
      throw new Error(`题库文件不存在: ${dataPath}`);
    }

    const rawData = fs.readFileSync(dataPath, "utf-8");
    const questions: QuestionData[] = JSON.parse(rawData);

    console.log(`📚 找到 ${questions.length} 道题目`);

    // 只在首次导入时才清理数据（此时数据库应该是空的）
    console.log("🧹 准备导入数据...");

    // 批量导入题目 - 使用事务提高性能
    console.log("📝 开始导入题目...");
    let imported = 0;
    const batchSize = 100;

    for (let i = 0; i < questions.length; i += batchSize) {
      const batch = questions.slice(i, i + batchSize);

      await prisma.$transaction(async tx => {
        for (const questionData of batch) {
          const category = categorizeQuestion(questionData.question_text);
          const difficulty = getDifficulty(questionData.question_text);

          await tx.question.create({
            data: {
              topic: questionData.topic,
              questionNumber: questionData.question_number,
              questionText: questionData.question_text,
              options: JSON.stringify(questionData.options),
              isMultipleChoice: questionData.is_multiple_choice,
              multipleChoiceCount: questionData.multiple_choice_count,
              correctAnswer: questionData.correct_answer,
              selectedAnswer: questionData.selected_answer,
              category,
              difficulty,
            },
          });
          imported++;
        }
      });

      if (imported % 100 === 0) {
        console.log(`✅ 已导入 ${imported} 道题目...`);
      }
    }

    console.log(`🎉 导入完成！总共导入 ${imported} 道题目`);

    // 统计信息
    const stats = await prisma.question.groupBy({
      by: ["category"],
      _count: {
        id: true,
      },
    });

    console.log("\n📊 分类统计:");
    stats.forEach(stat => {
      console.log(`  ${stat.category}: ${stat._count.id} 题`);
    });

    const difficultyStats = await prisma.question.groupBy({
      by: ["difficulty"],
      _count: {
        id: true,
      },
    });

    console.log("\n🎯 难度统计:");
    difficultyStats.forEach(stat => {
      console.log(`  ${stat.difficulty}: ${stat._count.id} 题`);
    });

    const multipleChoiceCount = await prisma.question.count({
      where: { isMultipleChoice: true },
    });

    const singleChoiceCount = await prisma.question.count({
      where: { isMultipleChoice: false },
    });

    console.log("\n📋 题型统计:");
    console.log(`  单选题: ${singleChoiceCount} 题`);
    console.log(`  多选题: ${multipleChoiceCount} 题`);
  } catch (error) {
    console.error("❌ 导入失败:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行导入
importQuestions();
