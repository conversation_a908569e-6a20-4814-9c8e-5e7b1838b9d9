export interface Question {
  id: string;
  topic: string;
  questionNumber: string;
  questionText: string;
  options: string[];
  isMultipleChoice: boolean;
  multipleChoiceCount: number;
  correctAnswer: string;
  selectedAnswer?: string;
  category?: string;
  difficulty?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Exam {
  id: string;
  userId?: string;
  examType: ExamType;
  title: string;
  totalQuestions: number;
  timeLimit?: number;
  status: ExamStatus;
  score?: number;
  correctCount?: number;
  startedAt: Date;
  completedAt?: Date;
}

export interface ExamQuestion {
  id: string;
  examId: string;
  questionId: string;
  order: number;
  question: Question;
}

export interface UserAnswer {
  id: string;
  userId?: string;
  examId: string;
  questionId: string;
  userAnswer: string;
  isCorrect: boolean;
  timeSpent?: number;
  answeredAt: Date;
}

export type ExamType = "simulation" | "practice" | "review";
export type ExamStatus = "in_progress" | "completed" | "paused";
export type QuestionDifficulty = "Easy" | "Medium" | "Hard";
export type QuestionCategory =
  | "Storage"
  | "Compute"
  | "Database"
  | "Serverless"
  | "Networking"
  | "Security"
  | "Monitoring"
  | "Content Delivery"
  | "API Management"
  | "Messaging"
  | "Management"
  | "Cost Management"
  | "General";

export interface ExamConfig {
  type: ExamType;
  questionCount: number;
  timeLimit?: number;
  categories?: QuestionCategory[];
  difficulty?: QuestionDifficulty[];
  includeMultipleChoice?: boolean;
}

export interface ExamResult {
  examId: string;
  score: number;
  correctCount: number;
  totalQuestions: number;
  timeSpent: number;
  categoryResults: CategoryResult[];
  difficultyResults: DifficultyResult[];
}

export interface CategoryResult {
  category: QuestionCategory;
  correct: number;
  total: number;
  percentage: number;
}

export interface DifficultyResult {
  difficulty: QuestionDifficulty;
  correct: number;
  total: number;
  percentage: number;
}

export interface ExamProgress {
  currentQuestionIndex: number;
  totalQuestions: number;
  answeredCount: number;
  timeRemaining?: number;
  markedQuestions: string[];
}
