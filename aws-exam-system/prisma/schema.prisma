// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 题目表
model Question {
  id                  String   @id @default(cuid())
  topic              String
  questionNumber     String
  questionText       String
  options            String   // JSON string of options array
  isMultipleChoice   Boolean
  multipleChoiceCount Int     @default(0)
  correctAnswer      String
  selectedAnswer     String?
  category           String?  // AWS服务分类
  difficulty         String?  // 难度等级
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // 关联
  examQuestions      ExamQuestion[]
  userAnswers        UserAnswer[]

  @@map("questions")
}

// 考试表
model Exam {
  id          String   @id @default(cuid())
  userId      String?
  examType    String   // "simulation", "practice", "review"
  title       String
  totalQuestions Int
  timeLimit   Int?     // 分钟
  status      String   // "in_progress", "completed", "paused"
  score       Float?
  correctCount Int?
  startedAt   DateTime @default(now())
  completedAt DateTime?

  // 关联
  user            User?          @relation(fields: [userId], references: [id])
  examQuestions   ExamQuestion[]
  userAnswers     UserAnswer[]

  @@map("exams")
}

// 考试题目关联表
model ExamQuestion {
  id         String @id @default(cuid())
  examId     String
  questionId String
  order      Int

  exam       Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  question   Question @relation(fields: [questionId], references: [id])

  @@unique([examId, questionId])
  @@map("exam_questions")
}

// 用户答题记录
model UserAnswer {
  id         String   @id @default(cuid())
  userId     String?
  examId     String
  questionId String
  userAnswer String   // 用户选择的答案
  isCorrect  Boolean
  timeSpent  Int?     // 秒
  answeredAt DateTime @default(now())

  user       User?    @relation(fields: [userId], references: [id])
  exam       Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  question   Question @relation(fields: [questionId], references: [id])

  // 关联
  answerCorrection AnswerCorrection?

  @@unique([examId, questionId])
  @@map("user_answers")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  createdAt DateTime @default(now())

  exams       Exam[]
  userAnswers UserAnswer[]

  @@map("users")
}
