#!/usr/bin/env tsx
/**
 * 调试题目数据
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function debugQuestions() {
  try {
    // 检查总题目数
    const totalCount = await prisma.question.count();
    console.log(`📊 总题目数: ${totalCount}`);

    // 检查分类分布
    const categories = await prisma.question.groupBy({
      by: ['category'],
      _count: {
        category: true,
      },
    });
    console.log('\n📂 分类分布:');
    categories.forEach(cat => {
      console.log(`  ${cat.category}: ${cat._count.category} 题`);
    });

    // 检查难度分布
    const difficulties = await prisma.question.groupBy({
      by: ['difficulty'],
      _count: {
        difficulty: true,
      },
    });
    console.log('\n🎯 难度分布:');
    difficulties.forEach(diff => {
      console.log(`  ${diff.difficulty}: ${diff._count.difficulty} 题`);
    });

    // 检查题型分布
    const types = await prisma.question.groupBy({
      by: ['isMultipleChoice'],
      _count: {
        isMultipleChoice: true,
      },
    });
    console.log('\n📝 题型分布:');
    types.forEach(type => {
      console.log(`  ${type.isMultipleChoice ? '多选题' : '单选题'}: ${type._count.isMultipleChoice} 题`);
    });

    // 测试无筛选条件的查询
    const allQuestions = await prisma.question.findMany({
      take: 5,
      select: {
        id: true,
        category: true,
        difficulty: true,
        isMultipleChoice: true,
      }
    });
    console.log('\n🔍 前5道题目样本:');
    allQuestions.forEach((q, i) => {
      console.log(`  ${i+1}. 分类: ${q.category}, 难度: ${q.difficulty}, 多选: ${q.isMultipleChoice}`);
    });

  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugQuestions();
