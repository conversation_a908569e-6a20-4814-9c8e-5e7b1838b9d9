"use server";

import { prisma } from "@/lib/prisma";
import { ExamConfig, ExamType } from "@/types/exam";
import { revalidatePath } from "next/cache";

/**
 * 创建新考试
 */
export async function createExam(config: ExamConfig) {
  try {
    // 根据配置筛选题目
    const whereClause: any = {};

    if (config.categories && config.categories.length > 0) {
      whereClause.category = { in: config.categories };
    }

    if (config.difficulty && config.difficulty.length > 0) {
      whereClause.difficulty = { in: config.difficulty };
    }

    if (config.includeMultipleChoice === false) {
      whereClause.isMultipleChoice = false;
    }

    // 随机选择题目
    const availableQuestions = await prisma.question.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "asc",
      },
    });

    if (availableQuestions.length < config.questionCount) {
      throw new Error(
        `可用题目不足，需要 ${config.questionCount} 题，但只有 ${availableQuestions.length} 题`
      );
    }

    // 随机打乱并选择指定数量的题目
    const shuffled = availableQuestions.sort(() => Math.random() - 0.5);
    const selectedQuestions = shuffled.slice(0, config.questionCount);

    // 创建考试
    const exam = await prisma.exam.create({
      data: {
        examType: config.type,
        title: getExamTitle(config.type),
        totalQuestions: config.questionCount,
        timeLimit: config.timeLimit,
        status: "in_progress",
      },
    });

    // 创建考试题目关联
    const examQuestions = selectedQuestions.map((question, index) => ({
      examId: exam.id,
      questionId: question.id,
      order: index + 1,
    }));

    await prisma.examQuestion.createMany({
      data: examQuestions,
    });

    return { success: true, examId: exam.id };
  } catch (error) {
    console.error("创建考试失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "创建考试失败",
    };
  }
}

/**
 * 获取考试详情
 */
export async function getExam(examId: string) {
  try {
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        examQuestions: {
          include: {
            question: true,
          },
          orderBy: {
            order: "asc",
          },
        },
        userAnswers: true,
      },
    });

    if (!exam) {
      throw new Error("考试不存在");
    }

    // 转换题目选项从JSON字符串到数组
    const questions = exam.examQuestions.map(eq => ({
      ...eq.question,
      options:
        typeof eq.question.options === "string"
          ? JSON.parse(eq.question.options)
          : eq.question.options,
    }));

    return {
      success: true,
      exam: {
        ...exam,
        questions,
      },
    };
  } catch (error) {
    console.error("获取考试失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "获取考试失败",
    };
  }
}

/**
 * 提交答案
 */
export async function submitAnswer(
  examId: string,
  questionId: string,
  userAnswer: string,
  timeSpent?: number
) {
  try {
    // 获取题目正确答案
    const question = await prisma.question.findUnique({
      where: { id: questionId },
    });

    if (!question) {
      throw new Error("题目不存在");
    }

    // 判断答案是否正确
    const isCorrect = userAnswer === question.correctAnswer;

    // 保存或更新用户答案
    await prisma.userAnswer.upsert({
      where: {
        examId_questionId: {
          examId,
          questionId,
        },
      },
      update: {
        userAnswer,
        isCorrect,
        timeSpent,
        answeredAt: new Date(),
      },
      create: {
        examId,
        questionId,
        userAnswer,
        isCorrect,
        timeSpent,
        answeredAt: new Date(),
      },
    });

    return { success: true, isCorrect };
  } catch (error) {
    console.error("提交答案失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "提交答案失败",
    };
  }
}

/**
 * 完成考试
 */
export async function completeExam(examId: string) {
  try {
    // 获取考试和答题记录
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        userAnswers: true,
        examQuestions: true,
      },
    });

    if (!exam) {
      throw new Error("考试不存在");
    }

    // 计算成绩
    const correctCount = exam.userAnswers.filter(
      answer => answer.isCorrect
    ).length;
    const score = (correctCount / exam.totalQuestions) * 100;

    // 更新考试状态
    await prisma.exam.update({
      where: { id: examId },
      data: {
        status: "completed",
        score,
        correctCount,
        completedAt: new Date(),
      },
    });

    revalidatePath(`/exam/${examId}`);

    return {
      success: true,
      result: {
        score,
        correctCount,
        totalQuestions: exam.totalQuestions,
      },
    };
  } catch (error) {
    console.error("完成考试失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "完成考试失败",
    };
  }
}

/**
 * 获取考试结果详情
 */
export async function getExamResult(examId: string) {
  try {
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        userAnswers: {
          include: {
            question: true,
          },
        },
        examQuestions: {
          include: {
            question: true,
          },
        },
      },
    });

    if (!exam) {
      throw new Error("考试不存在");
    }

    // 计算分类统计
    const categoryStats = new Map();
    const difficultyStats = new Map();

    exam.examQuestions.forEach(eq => {
      const question = eq.question;
      const userAnswer = exam.userAnswers.find(
        ua => ua.questionId === question.id
      );
      const isCorrect = userAnswer?.isCorrect || false;

      // 分类统计
      const category = question.category || "General";
      if (!categoryStats.has(category)) {
        categoryStats.set(category, { correct: 0, total: 0 });
      }
      const catStat = categoryStats.get(category);
      catStat.total++;
      if (isCorrect) catStat.correct++;

      // 难度统计
      const difficulty = question.difficulty || "Medium";
      if (!difficultyStats.has(difficulty)) {
        difficultyStats.set(difficulty, { correct: 0, total: 0 });
      }
      const diffStat = difficultyStats.get(difficulty);
      diffStat.total++;
      if (isCorrect) diffStat.correct++;
    });

    const categoryResults = Array.from(categoryStats.entries()).map(
      ([category, stats]) => ({
        category,
        correct: stats.correct,
        total: stats.total,
        percentage: Math.round((stats.correct / stats.total) * 100),
      })
    );

    const difficultyResults = Array.from(difficultyStats.entries()).map(
      ([difficulty, stats]) => ({
        difficulty,
        correct: stats.correct,
        total: stats.total,
        percentage: Math.round((stats.correct / stats.total) * 100),
      })
    );

    return {
      success: true,
      result: {
        examId: exam.id,
        score: exam.score || 0,
        correctCount: exam.correctCount || 0,
        totalQuestions: exam.totalQuestions,
        timeSpent: Math.round(
          (new Date(exam.completedAt!).getTime() -
            new Date(exam.startedAt).getTime()) /
            1000
        ),
        categoryResults,
        difficultyResults,
      },
    };
  } catch (error) {
    console.error("获取考试结果失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "获取考试结果失败",
    };
  }
}

/**
 * 获取所有题目（用于管理）
 */
export async function getAllQuestions() {
  try {
    const questions = await prisma.question.findMany({
      orderBy: { id: "asc" },
    });

    return {
      success: true,
      questions: questions.map((q, index) => ({
        id: q.id,
        questionNumber: index + 1,
        questionText: q.questionText,
        options:
          typeof q.options === "string" ? JSON.parse(q.options) : q.options,
        correctAnswer: q.correctAnswer,
        isMultipleChoice: q.isMultipleChoice,
        multipleChoiceCount: q.multipleChoiceCount,
        category: q.category,
        difficulty: q.difficulty,
      })),
    };
  } catch (error) {
    console.error("获取题目失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "获取题目失败",
    };
  }
}

/**
 * 更新题目答案
 */
export async function updateQuestionAnswer(
  questionId: string,
  newAnswer: string
) {
  try {
    await prisma.question.update({
      where: { id: questionId },
      data: { correctAnswer: newAnswer },
    });

    return { success: true };
  } catch (error) {
    console.error("更新题目答案失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "更新题目答案失败",
    };
  }
}

/**
 * 重新计算所有考试结果
 */
export async function recalculateAllExamResults() {
  try {
    const exams = await prisma.exam.findMany({
      where: { status: "completed" },
      include: {
        userAnswers: {
          include: {
            question: true,
          },
        },
      },
    });

    for (const exam of exams) {
      let correctCount = 0;

      // 重新计算每个答案的正确性
      for (const userAnswer of exam.userAnswers) {
        const isCorrect =
          userAnswer.userAnswer === userAnswer.question.correctAnswer;

        // 更新用户答案的正确性
        await prisma.userAnswer.update({
          where: { id: userAnswer.id },
          data: { isCorrect },
        });

        if (isCorrect) correctCount++;
      }

      // 重新计算分数
      const score = Math.round((correctCount / exam.totalQuestions) * 100);

      // 更新考试结果
      await prisma.exam.update({
        where: { id: exam.id },
        data: {
          score,
          correctCount,
        },
      });
    }

    return {
      success: true,
      message: `已重新计算 ${exams.length} 场考试的结果`,
    };
  } catch (error) {
    console.error("重新计算考试结果失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "重新计算考试结果失败",
    };
  }
}

/**
 * 获取考试历史
 */
export async function getExamHistory() {
  try {
    const exams = await prisma.exam.findMany({
      orderBy: { startedAt: "desc" },
      take: 50, // 限制返回最近50次考试
    });

    return {
      success: true,
      exams: exams.map(exam => ({
        id: exam.id,
        title: exam.title,
        type: exam.examType,
        status: exam.status,
        score: exam.score,
        totalQuestions: exam.totalQuestions,
        correctCount: exam.correctCount,
        timeSpent:
          exam.completedAt && exam.startedAt
            ? Math.round(
                (new Date(exam.completedAt).getTime() -
                  new Date(exam.startedAt).getTime()) /
                  1000
              )
            : null,
        createdAt: exam.startedAt.toISOString(),
        completedAt: exam.completedAt?.toISOString() || null,
      })),
    };
  } catch (error) {
    console.error("获取考试历史失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "获取考试历史失败",
    };
  }
}

/**
 * 获取考试题目回顾
 */
export async function getExamReview(examId: string) {
  try {
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        userAnswers: {
          include: {
            question: true,
            answerCorrection: true,
          },
        },
        examQuestions: {
          include: {
            question: true,
          },
          orderBy: { order: "asc" },
        },
      },
    });

    if (!exam) {
      throw new Error("考试不存在");
    }

    const questions = exam.examQuestions.map((eq, index) => {
      const userAnswer = exam.userAnswers.find(
        ua => ua.questionId === eq.question.id
      );

      return {
        id: eq.question.id,
        questionNumber: index + 1,
        questionText: eq.question.questionText,
        options:
          typeof eq.question.options === "string"
            ? JSON.parse(eq.question.options)
            : eq.question.options,
        correctAnswer: eq.question.correctAnswer,
        userAnswer: userAnswer?.userAnswer || "",
        isCorrect: userAnswer?.isCorrect || false,
        isMultipleChoice: eq.question.isMultipleChoice,
        multipleChoiceCount: eq.question.multipleChoiceCount,
        category: eq.question.category,
        difficulty: eq.question.difficulty,
        userAnswerId: userAnswer?.id,
        correctedAnswer: userAnswer?.answerCorrection?.correctedAnswer || null,
        correctionExplanation: userAnswer?.answerCorrection?.explanation || null,
      };
    });

    return {
      success: true,
      questions,
    };
  } catch (error) {
    console.error("获取考试回顾失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "获取考试回顾失败",
    };
  }
}

// 辅助函数
function getExamTitle(type: ExamType): string {
  switch (type) {
    case "simulation":
      return "AWS SAA-C03 模拟考试";
    case "practice":
      return "AWS SAA-C03 练习模式";
    case "review":
      return "AWS SAA-C03 错题复习";
    default:
      return "AWS SAA-C03 考试";
  }
}
