"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  getAllQuestions,
  updateQuestionAnswer,
  recalculateAllExamResults,
} from "@/lib/actions/exam-actions";
import { Search, Edit, Save, X, RefreshCw } from "lucide-react";

interface Question {
  id: string;
  questionNumber: number;
  questionText: string;
  options: string[];
  correctAnswer: string;
  isMultipleChoice: boolean;
  multipleChoiceCount?: number;
  category: string | null;
  difficulty: string | null;
}

export default function QuestionsAdminPage() {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingQuestion, setEditingQuestion] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<Question>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [recalculating, setRecalculating] = useState(false);

  useEffect(() => {
    loadQuestions();
  }, []);

  useEffect(() => {
    const filtered = questions.filter(
      q =>
        q.questionText.toLowerCase().includes(searchTerm.toLowerCase()) ||
        q.questionNumber.toString().includes(searchTerm) ||
        q.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredQuestions(filtered);
  }, [questions, searchTerm]);

  const loadQuestions = async () => {
    try {
      const result = await getAllQuestions();
      if (result.success && result.questions) {
        setQuestions(result.questions);
      } else {
        console.error("加载题目失败:", result.error);
        alert("加载题目失败: " + result.error);
      }
    } catch (error) {
      console.error("加载题目失败:", error);
      alert("加载题目失败");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (question: Question) => {
    setEditingQuestion(question.id);
    setEditForm(question);
  };

  const handleSave = async () => {
    if (!editingQuestion || !editForm.correctAnswer) return;

    setSaving(true);
    try {
      const result = await updateQuestionAnswer(
        editingQuestion,
        editForm.correctAnswer
      );
      if (result.success) {
        // 更新本地状态
        setQuestions(prev =>
          prev.map(q =>
            q.id === editingQuestion ? ({ ...q, ...editForm } as Question) : q
          )
        );

        setEditingQuestion(null);
        setEditForm({});
        alert("题目修改已保存");
      } else {
        alert("保存失败: " + result.error);
      }
    } catch (error) {
      console.error("保存失败:", error);
      alert("保存失败");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingQuestion(null);
    setEditForm({});
  };

  const handleRecalculateResults = async () => {
    if (!confirm("确定要重新计算所有考试结果吗？这可能需要一些时间。")) {
      return;
    }

    setRecalculating(true);
    try {
      const result = await recalculateAllExamResults();
      if (result.success) {
        alert(result.message || "所有考试结果已重新计算完成");
      } else {
        alert("重新计算失败: " + result.error);
      }
    } catch (error) {
      console.error("重新计算失败:", error);
      alert("重新计算失败");
    } finally {
      setRecalculating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="text-gray-600">加载题目中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mx-auto max-w-6xl">
          {/* Header */}
          <div className="mb-8 flex items-center justify-between">
            <div>
              <h1 className="mb-2 text-3xl font-bold text-gray-900">
                题目管理
              </h1>
              <p className="text-gray-600">管理和校准考试题目的正确答案</p>
            </div>

            <Button
              onClick={handleRecalculateResults}
              disabled={recalculating}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${recalculating ? "animate-spin" : ""}`}
              />
              {recalculating ? "重新计算中..." : "重新计算所有结果"}
            </Button>
          </div>

          {/* Search */}
          <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索题目编号、内容或分类..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="w-full rounded-md border border-gray-300 py-2 pr-4 pl-10 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                />
              </div>
              <div className="text-sm text-gray-600">
                共 {filteredQuestions.length} 道题目
              </div>
            </div>
          </div>

          {/* Questions List */}
          <div className="space-y-4">
            {filteredQuestions.map(question => (
              <div
                key={question.id}
                className="rounded-lg border border-gray-200 bg-white shadow-sm"
              >
                {editingQuestion === question.id ? (
                  // Edit Mode
                  <div className="p-6">
                    <div className="mb-4 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium text-gray-500">
                          题目 {question.questionNumber}
                        </span>
                        <span className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-700">
                          {question.category}
                        </span>
                        <span className="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700">
                          {question.difficulty}
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          onClick={handleSave}
                          disabled={saving}
                        >
                          <Save className="mr-1 h-4 w-4" />
                          {saving ? "保存中..." : "保存"}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCancel}
                        >
                          <X className="mr-1 h-4 w-4" />
                          取消
                        </Button>
                      </div>
                    </div>

                    <div className="mb-4">
                      <p className="leading-relaxed text-gray-900">
                        {question.questionText}
                      </p>
                    </div>

                    <div className="mb-4 space-y-2">
                      {question.options.map((option, index) => (
                        <div key={index} className="text-gray-700">
                          {option}
                        </div>
                      ))}
                    </div>

                    <div className="rounded-lg bg-yellow-50 p-4">
                      <label className="mb-2 block text-sm font-medium text-gray-700">
                        正确答案 (当前: {question.correctAnswer})
                      </label>
                      <input
                        type="text"
                        value={editForm.correctAnswer || ""}
                        onChange={e =>
                          setEditForm(prev => ({
                            ...prev,
                            correctAnswer: e.target.value.toUpperCase(),
                          }))
                        }
                        placeholder="输入正确答案，如: A 或 AB"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        单选题输入单个字母(如: A)，多选题输入多个字母(如: AB)
                      </p>
                    </div>
                  </div>
                ) : (
                  // View Mode
                  <div className="p-6">
                    <div className="mb-4 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium text-gray-500">
                          题目 {question.questionNumber}
                        </span>
                        {question.isMultipleChoice && (
                          <span className="rounded bg-orange-100 px-2 py-1 text-xs text-orange-700">
                            多选题
                          </span>
                        )}
                        <span className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-700">
                          {question.category}
                        </span>
                        <span className="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700">
                          {question.difficulty}
                        </span>
                      </div>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(question)}
                      >
                        <Edit className="mr-1 h-4 w-4" />
                        编辑
                      </Button>
                    </div>

                    <div className="mb-4">
                      <p className="leading-relaxed text-gray-900">
                        {question.questionText}
                      </p>
                    </div>

                    <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <h4 className="mb-2 text-sm font-medium text-gray-700">
                          选项:
                        </h4>
                        <div className="space-y-1">
                          {question.options.map((option, index) => (
                            <div key={index} className="text-sm text-gray-600">
                              {option}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="mb-2 text-sm font-medium text-gray-700">
                          正确答案:
                        </h4>
                        <div className="text-lg font-bold text-green-600">
                          {question.correctAnswer}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredQuestions.length === 0 && (
            <div className="rounded-lg bg-white p-12 text-center shadow-sm">
              <div className="mb-4 text-gray-400">
                <Search className="mx-auto h-16 w-16" />
              </div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                没有找到匹配的题目
              </h3>
              <p className="text-gray-600">请尝试其他搜索关键词</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
