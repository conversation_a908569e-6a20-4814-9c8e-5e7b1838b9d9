import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Clock, Target, RotateCcw, History } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="mb-16 text-center">
          <h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-6xl">
            AWS SAA-C03
            <span className="block text-blue-600">模拟考试系统</span>
          </h1>
          <p className="mx-auto max-w-2xl text-xl text-gray-600">
            基于1,018道真题的专业AWS解决方案架构师认证考试练习平台
          </p>
        </div>

        {/* Stats */}
        <div className="mb-16 grid grid-cols-1 gap-6 md:grid-cols-4">
          <div className="rounded-lg bg-white p-6 text-center shadow-sm">
            <div className="mb-2 text-3xl font-bold text-blue-600">1,018</div>
            <div className="text-gray-600">道真题</div>
          </div>
          <div className="rounded-lg bg-white p-6 text-center shadow-sm">
            <div className="mb-2 text-3xl font-bold text-green-600">13</div>
            <div className="text-gray-600">个分类</div>
          </div>
          <div className="rounded-lg bg-white p-6 text-center shadow-sm">
            <div className="mb-2 text-3xl font-bold text-purple-600">88%</div>
            <div className="text-gray-600">单选题</div>
          </div>
          <div className="rounded-lg bg-white p-6 text-center shadow-sm">
            <div className="mb-2 text-3xl font-bold text-orange-600">12%</div>
            <div className="text-gray-600">多选题</div>
          </div>
        </div>

        {/* Exam Modes */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {/* 模拟考试 */}
          <div className="rounded-xl bg-white p-8 shadow-lg transition-shadow hover:shadow-xl">
            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
              <Target className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="mb-4 text-center text-xl font-semibold text-gray-900">
              模拟考试
            </h3>
            <p className="mb-6 text-center text-gray-600">
              65题，130分钟，完全模拟真实考试环境
            </p>
            <Link href="/exam/create?type=simulation" className="block">
              <Button className="w-full bg-blue-600 hover:bg-blue-700">
                开始模拟考试
              </Button>
            </Link>
          </div>

          {/* 练习模式 */}
          <div className="rounded-xl bg-white p-8 shadow-lg transition-shadow hover:shadow-xl">
            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <BookOpen className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="mb-4 text-center text-xl font-semibold text-gray-900">
              练习模式
            </h3>
            <p className="mb-6 text-center text-gray-600">
              自定义题目数量和分类，灵活练习
            </p>
            <Link href="/exam/create?type=practice" className="block">
              <Button className="w-full bg-green-600 hover:bg-green-700">
                开始练习
              </Button>
            </Link>
          </div>

          {/* 分类练习 */}
          <div className="rounded-xl bg-white p-8 shadow-lg transition-shadow hover:shadow-xl">
            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="mb-4 text-center text-xl font-semibold text-gray-900">
              分类练习
            </h3>
            <p className="mb-6 text-center text-gray-600">
              按AWS服务分类进行专项练习
            </p>
            <Link href="/practice/categories" className="block">
              <Button className="w-full bg-purple-600 hover:bg-purple-700">
                选择分类
              </Button>
            </Link>
          </div>

          {/* 错题复习 */}
          <div className="rounded-xl bg-white p-8 shadow-lg transition-shadow hover:shadow-xl">
            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100">
              <RotateCcw className="h-8 w-8 text-orange-600" />
            </div>
            <h3 className="mb-4 text-center text-xl font-semibold text-gray-900">
              错题复习
            </h3>
            <p className="mb-6 text-center text-gray-600">
              针对错题进行专项复习和强化
            </p>
            <Link href="/exam/create?type=review" className="block">
              <Button className="w-full bg-orange-600 hover:bg-orange-700">
                复习错题
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-16 text-center">
          <div className="mb-12 flex justify-center gap-4">
            <Link href="/history">
              <Button variant="outline" className="flex items-center gap-2">
                <History className="h-4 w-4" />
                考试历史
              </Button>
            </Link>
            <Link href="/admin/questions">
              <Button variant="outline" className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                题目管理
              </Button>
            </Link>
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 text-center">
          <h2 className="mb-8 text-2xl font-semibold text-gray-900">
            系统特色
          </h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mb-4 text-4xl">🎯</div>
              <h3 className="mb-2 text-lg font-semibold">精准题库</h3>
              <p className="text-gray-600">
                基于最新AWS SAA-C03考试大纲，覆盖所有知识点
              </p>
            </div>
            <div className="text-center">
              <div className="mb-4 text-4xl">📊</div>
              <h3 className="mb-2 text-lg font-semibold">智能分析</h3>
              <p className="text-gray-600">详细的成绩分析和薄弱环节识别</p>
            </div>
            <div className="text-center">
              <div className="mb-4 text-4xl">⚡</div>
              <h3 className="mb-2 text-lg font-semibold">实时反馈</h3>
              <p className="text-gray-600">即时答题反馈和进度跟踪</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
