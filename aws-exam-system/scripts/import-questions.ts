#!/usr/bin/env tsx
/**
 * 导入AWS题库数据到数据库
 */

import { PrismaClient } from "@prisma/client";
import fs from "fs";
import path from "path";

const prisma = new PrismaClient();

interface QuestionData {
  topic: string;
  question_number: string;
  question_text: string;
  options: string[];
  is_multiple_choice: boolean;
  multiple_choice_count: number;
  correct_answer: string;
  selected_answer?: string;
}

// AWS服务分类映射
const getCategory = (questionText: string): string => {
  const text = questionText.toLowerCase();

  if (text.includes("s3") || text.includes("simple storage")) return "Storage";
  if (text.includes("ec2") || text.includes("elastic compute"))
    return "Compute";
  if (
    text.includes("rds") ||
    text.includes("aurora") ||
    text.includes("database")
  )
    return "Database";
  if (text.includes("lambda") || text.includes("serverless"))
    return "Serverless";
  if (
    text.includes("vpc") ||
    text.includes("network") ||
    text.includes("load balancer")
  )
    return "Networking";
  if (
    text.includes("iam") ||
    text.includes("security") ||
    text.includes("access")
  )
    return "Security";
  if (text.includes("cloudwatch") || text.includes("monitoring"))
    return "Monitoring";
  if (text.includes("cloudfront") || text.includes("cdn"))
    return "Content Delivery";
  if (text.includes("api gateway") || text.includes("rest api"))
    return "API Management";
  if (text.includes("sqs") || text.includes("sns") || text.includes("kinesis"))
    return "Messaging";
  if (text.includes("organizations") || text.includes("account"))
    return "Management";
  if (text.includes("cost") || text.includes("billing"))
    return "Cost Management";

  return "General";
};

// 难度评估
const getDifficulty = (
  questionText: string,
  isMultipleChoice: boolean
): string => {
  const text = questionText.toLowerCase();

  // 多选题通常更难
  if (isMultipleChoice) return "Hard";

  // 包含复杂概念的题目
  if (
    text.includes("cross-region") ||
    text.includes("multi-az") ||
    text.includes("encryption") ||
    text.includes("compliance")
  ) {
    return "Hard";
  }

  // 包含架构设计的题目
  if (
    text.includes("architecture") ||
    text.includes("design") ||
    text.includes("solution") ||
    text.includes("best practice")
  ) {
    return "Medium";
  }

  return "Easy";
};

async function importQuestions() {
  try {
    // 检查数据库是否已有数据
    const existingQuestions = await prisma.question.count();

    if (existingQuestions > 0) {
      console.log(`📚 数据库已有 ${existingQuestions} 道题目，跳过导入`);
      console.log("💡 如需重新导入，请先手动清空数据库或删除容器卷");
      return;
    }

    console.log("🚀 开始导入AWS题库数据...");

    // 读取题库数据
    const dataPath = path.join(process.cwd(), "aws_quiz_final.json");

    if (!fs.existsSync(dataPath)) {
      throw new Error(`题库文件不存在: ${dataPath}`);
    }

    const rawData = fs.readFileSync(dataPath, "utf-8");
    const questions: QuestionData[] = JSON.parse(rawData);

    console.log(`📚 找到 ${questions.length} 道题目`);

    // 只在首次导入时才清理数据（此时数据库应该是空的）
    console.log("🧹 准备导入数据...");

    // 批量导入题目 - 使用事务提高性能
    console.log("📝 开始导入题目...");
    let imported = 0;
    const batchSize = 100;

    // 分批处理以提高性能
    for (let i = 0; i < questions.length; i += batchSize) {
      const batch = questions.slice(i, i + batchSize);

      try {
        await prisma.$transaction(async tx => {
          for (const questionData of batch) {
            const category = getCategory(questionData.question_text);
            const difficulty = getDifficulty(
              questionData.question_text,
              questionData.is_multiple_choice
            );

            await tx.question.create({
              data: {
                topic: questionData.topic,
                questionNumber: questionData.question_number,
                questionText: questionData.question_text,
                options: JSON.stringify(questionData.options),
                isMultipleChoice: questionData.is_multiple_choice,
                multipleChoiceCount: questionData.multiple_choice_count,
                correctAnswer: questionData.correct_answer,
                selectedAnswer: questionData.selected_answer,
                category,
                difficulty,
              },
            });

            imported++;
          }
        });

        console.log(`✅ 已导入 ${imported} 道题目...`);
      } catch (error) {
        console.error(`❌ 导入批次失败 (${i}-${i + batch.length}):`, error);
      }
    }

    console.log(`🎉 导入完成！总共导入 ${imported} 道题目`);

    // 统计信息
    const stats = await prisma.question.groupBy({
      by: ["category"],
      _count: {
        id: true,
      },
    });

    console.log("\n📊 分类统计:");
    stats.forEach(stat => {
      console.log(`  ${stat.category}: ${stat._count.id} 题`);
    });

    const difficultyStats = await prisma.question.groupBy({
      by: ["difficulty"],
      _count: {
        id: true,
      },
    });

    console.log("\n🎯 难度统计:");
    difficultyStats.forEach(stat => {
      console.log(`  ${stat.difficulty}: ${stat._count.id} 题`);
    });

    const multipleChoiceCount = await prisma.question.count({
      where: { isMultipleChoice: true },
    });

    const singleChoiceCount = await prisma.question.count({
      where: { isMultipleChoice: false },
    });

    console.log("\n📋 题型统计:");
    console.log(`  单选题: ${singleChoiceCount} 题`);
    console.log(`  多选题: ${multipleChoiceCount} 题`);
  } catch (error) {
    console.error("❌ 导入失败:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行导入
if (require.main === module) {
  importQuestions();
}
